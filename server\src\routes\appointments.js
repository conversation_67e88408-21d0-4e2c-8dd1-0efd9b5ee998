const express = require('express');
const { body, param, query } = require('express-validator');
const { executeQuery } = require('../config/database');
const { handleValidationErrors } = require('../middleware/errorHandler');
const { authenticateToken, requireBusiness } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const appointmentValidation = [
  body('customerName').trim().isLength({ min: 1, max: 255 }).withMessage('Customer name is required'),
  body('customerEmail').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('customerPhone').optional().trim().isLength({ max: 20 }).withMessage('Phone number must be less than 20 characters'),
  body('serviceId').isInt({ min: 1 }).withMessage('Valid service ID is required'),
  body('appointmentDate').isDate().withMessage('Valid appointment date is required'),
  body('appointmentTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid appointment time is required (HH:MM format)')
];

const appointmentIdValidation = [
  param('id').isInt({ min: 1 }).withMessage('Invalid appointment ID')
];

const statusValidation = [
  body('status').isIn(['pending', 'confirmed', 'cancelled', 'completed', 'no_show']).withMessage('Invalid status')
];

// Helper function to calculate end time
const calculateEndTime = (startTime, durationMinutes) => {
  const [hours, minutes] = startTime.split(':').map(Number);
  const startMinutes = hours * 60 + minutes;
  const endMinutes = startMinutes + durationMinutes;
  const endHours = Math.floor(endMinutes / 60);
  const endMins = endMinutes % 60;
  return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
};

// Helper function to check for time conflicts
const checkTimeConflict = async (businessId, serviceId, appointmentDate, appointmentTime, excludeId = null) => {
  // Get service duration
  const service = await executeQuery(
    'SELECT duration_minutes FROM services WHERE id = ?',
    [serviceId]
  );
  
  if (service.length === 0) {
    throw new Error('Service not found');
  }

  const durationMinutes = service[0].duration_minutes;
  const endTime = calculateEndTime(appointmentTime, durationMinutes);

  // Check for conflicts
  let query = `
    SELECT id FROM appointments 
    WHERE business_id = ? AND appointment_date = ? 
    AND status NOT IN ('cancelled', 'no_show')
    AND (
      (appointment_time <= ? AND end_time > ?) OR
      (appointment_time < ? AND end_time >= ?)
    )
  `;
  let params = [businessId, appointmentDate, appointmentTime, appointmentTime, endTime, endTime];

  if (excludeId) {
    query += ' AND id != ?';
    params.push(excludeId);
  }

  const conflicts = await executeQuery(query, params);
  return conflicts.length > 0;
};

// Create appointment (public endpoint for booking)
router.post('/book/:businessSlug', appointmentValidation, handleValidationErrors, async (req, res) => {
  try {
    const { businessSlug } = req.params;
    const { customerName, customerEmail, customerPhone, serviceId, appointmentDate, appointmentTime } = req.body;

    // Get business info
    const business = await executeQuery(
      'SELECT id FROM businesses WHERE slug = ? AND is_active = 1',
      [businessSlug]
    );

    if (business.length === 0) {
      return res.status(404).json({
        error: 'Business not found',
        message: 'The requested business does not exist'
      });
    }

    const businessId = business[0].id;

    // Verify service belongs to business
    const service = await executeQuery(
      'SELECT id, name, duration_minutes FROM services WHERE id = ? AND business_id = ? AND is_active = 1',
      [serviceId, businessId]
    );

    if (service.length === 0) {
      return res.status(404).json({
        error: 'Service not found',
        message: 'The requested service is not available'
      });
    }

    // Check for time conflicts
    const hasConflict = await checkTimeConflict(businessId, serviceId, appointmentDate, appointmentTime);
    
    if (hasConflict) {
      return res.status(409).json({
        error: 'Time slot unavailable',
        message: 'The selected time slot is already booked'
      });
    }

    // Calculate end time
    const endTime = calculateEndTime(appointmentTime, service[0].duration_minutes);

    // Create appointment
    const result = await executeQuery(`
      INSERT INTO appointments 
      (business_id, service_id, customer_name, customer_email, customer_phone, 
       appointment_date, appointment_time, end_time, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
    `, [businessId, serviceId, customerName, customerEmail, customerPhone || null, 
        appointmentDate, appointmentTime, endTime]);

    // Get the created appointment with service details
    const newAppointment = await executeQuery(`
      SELECT a.id, a.customer_name as customerName, a.customer_email as customerEmail,
             a.customer_phone as customerPhone, a.appointment_date as appointmentDate,
             a.appointment_time as appointmentTime, a.status, a.created_at as createdAt,
             s.id as serviceId, s.name as serviceName, s.price, s.duration_minutes as durationMinutes
      FROM appointments a
      JOIN services s ON a.service_id = s.id
      WHERE a.id = ?
    `, [result.insertId]);

    res.status(201).json({
      message: 'Appointment booked successfully',
      appointment: newAppointment[0]
    });

  } catch (error) {
    console.error('Book appointment error:', error);
    res.status(500).json({
      error: 'Failed to book appointment',
      message: 'An error occurred while booking the appointment'
    });
  }
});

// Get all appointments for business owner
router.get('/', authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { date, status, limit = 50, offset = 0 } = req.query;

    let query = `
      SELECT a.id, a.customer_name as customerName, a.customer_email as customerEmail,
             a.customer_phone as customerPhone, a.appointment_date as appointmentDate,
             a.appointment_time as appointmentTime, a.end_time as endTime, a.status, 
             a.notes, a.created_at as createdAt, a.updated_at as updatedAt,
             s.id as serviceId, s.name as serviceName, s.price, s.duration_minutes as durationMinutes
      FROM appointments a
      JOIN services s ON a.service_id = s.id
      WHERE a.business_id = ?
    `;
    let params = [req.user.businessId];

    // Add filters
    if (date) {
      query += ' AND a.appointment_date = ?';
      params.push(date);
    }

    if (status) {
      query += ' AND a.status = ?';
      params.push(status);
    }

    query += ' ORDER BY a.appointment_date DESC, a.appointment_time DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const appointments = await executeQuery(query, params);

    res.json(appointments);

  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({
      error: 'Failed to fetch appointments',
      message: 'An error occurred while fetching appointments'
    });
  }
});

// Get single appointment
router.get('/:id', appointmentIdValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;

    const appointments = await executeQuery(`
      SELECT a.id, a.customer_name as customerName, a.customer_email as customerEmail,
             a.customer_phone as customerPhone, a.appointment_date as appointmentDate,
             a.appointment_time as appointmentTime, a.end_time as endTime, a.status, 
             a.notes, a.created_at as createdAt, a.updated_at as updatedAt,
             s.id as serviceId, s.name as serviceName, s.price, s.duration_minutes as durationMinutes
      FROM appointments a
      JOIN services s ON a.service_id = s.id
      WHERE a.id = ? AND a.business_id = ?
    `, [id, req.user.businessId]);

    if (appointments.length === 0) {
      return res.status(404).json({
        error: 'Appointment not found',
        message: 'The requested appointment does not exist'
      });
    }

    res.json(appointments[0]);

  } catch (error) {
    console.error('Get appointment error:', error);
    res.status(500).json({
      error: 'Failed to fetch appointment',
      message: 'An error occurred while fetching the appointment'
    });
  }
});

// Update appointment status
router.patch('/:id/status', appointmentIdValidation, statusValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Check if appointment exists and belongs to user's business
    const existingAppointment = await executeQuery(
      'SELECT id, status FROM appointments WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    if (existingAppointment.length === 0) {
      return res.status(404).json({
        error: 'Appointment not found',
        message: 'The requested appointment does not exist'
      });
    }

    await executeQuery(
      'UPDATE appointments SET status = ? WHERE id = ? AND business_id = ?',
      [status, id, req.user.businessId]
    );

    res.json({
      message: 'Appointment status updated successfully',
      status
    });

  } catch (error) {
    console.error('Update appointment status error:', error);
    res.status(500).json({
      error: 'Failed to update appointment status',
      message: 'An error occurred while updating the appointment status'
    });
  }
});

// Update appointment notes
router.patch('/:id/notes', appointmentIdValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    // Check if appointment exists and belongs to user's business
    const existingAppointment = await executeQuery(
      'SELECT id FROM appointments WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    if (existingAppointment.length === 0) {
      return res.status(404).json({
        error: 'Appointment not found',
        message: 'The requested appointment does not exist'
      });
    }

    await executeQuery(
      'UPDATE appointments SET notes = ? WHERE id = ? AND business_id = ?',
      [notes || null, id, req.user.businessId]
    );

    res.json({
      message: 'Appointment notes updated successfully'
    });

  } catch (error) {
    console.error('Update appointment notes error:', error);
    res.status(500).json({
      error: 'Failed to update appointment notes',
      message: 'An error occurred while updating the appointment notes'
    });
  }
});

// Delete appointment
router.delete('/:id', appointmentIdValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if appointment exists and belongs to user's business
    const existingAppointment = await executeQuery(
      'SELECT id, status FROM appointments WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    if (existingAppointment.length === 0) {
      return res.status(404).json({
        error: 'Appointment not found',
        message: 'The requested appointment does not exist'
      });
    }

    // Only allow deletion of pending or cancelled appointments
    if (!['pending', 'cancelled'].includes(existingAppointment[0].status)) {
      return res.status(400).json({
        error: 'Cannot delete appointment',
        message: 'Only pending or cancelled appointments can be deleted'
      });
    }

    await executeQuery(
      'DELETE FROM appointments WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    res.json({
      message: 'Appointment deleted successfully'
    });

  } catch (error) {
    console.error('Delete appointment error:', error);
    res.status(500).json({
      error: 'Failed to delete appointment',
      message: 'An error occurred while deleting the appointment'
    });
  }
});

// Get available time slots for a specific date and service
router.get('/availability/:businessSlug/:serviceId/:date', async (req, res) => {
  try {
    const { businessSlug, serviceId, date } = req.params;

    // Get business info
    const business = await executeQuery(
      'SELECT id, business_hours FROM businesses WHERE slug = ? AND is_active = 1',
      [businessSlug]
    );

    if (business.length === 0) {
      return res.status(404).json({
        error: 'Business not found',
        message: 'The requested business does not exist'
      });
    }

    // Get service info
    const service = await executeQuery(
      'SELECT duration_minutes FROM services WHERE id = ? AND business_id = ? AND is_active = 1',
      [serviceId, business[0].id]
    );

    if (service.length === 0) {
      return res.status(404).json({
        error: 'Service not found',
        message: 'The requested service is not available'
      });
    }

    // Get existing appointments for the date
    const existingAppointments = await executeQuery(`
      SELECT appointment_time, end_time
      FROM appointments
      WHERE business_id = ? AND appointment_date = ?
      AND status NOT IN ('cancelled', 'no_show')
      ORDER BY appointment_time
    `, [business[0].id, date]);

    // Generate available time slots (simplified - you can enhance this with business hours)
    const availableSlots = [];
    const startHour = 9; // 9 AM
    const endHour = 17; // 5 PM
    const slotInterval = 30; // 30 minutes

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += slotInterval) {
        const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const endTime = calculateEndTime(timeSlot, service[0].duration_minutes);

        // Check if this slot conflicts with existing appointments
        const hasConflict = existingAppointments.some(apt => {
          return (timeSlot >= apt.appointment_time && timeSlot < apt.end_time) ||
                 (endTime > apt.appointment_time && endTime <= apt.end_time) ||
                 (timeSlot <= apt.appointment_time && endTime >= apt.end_time);
        });

        if (!hasConflict) {
          availableSlots.push(timeSlot);
        }
      }
    }

    res.json({
      date,
      availableSlots
    });

  } catch (error) {
    console.error('Get availability error:', error);
    res.status(500).json({
      error: 'Failed to fetch availability',
      message: 'An error occurred while fetching available time slots'
    });
  }
});

module.exports = router;
