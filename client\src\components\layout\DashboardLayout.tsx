import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Calendar, Users, Settings, Grid, LogOut } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
interface DashboardLayoutProps {
  children: React.ReactNode;
}
const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children
}) => {
  const location = useLocation();
  const {
    currentUser,
    logout
  } = useAuth();
  const navItems = [{
    name: 'Dashboard',
    path: '/dashboard',
    icon: <Grid className="w-5 h-5" />
  }, {
    name: 'Appointments',
    path: '/dashboard/appointments',
    icon: <Calendar className="w-5 h-5" />
  }, {
    name: 'Services',
    path: '/dashboard/services',
    icon: <Users className="w-5 h-5" />
  }, {
    name: 'Settings',
    path: '/dashboard/settings',
    icon: <Settings className="w-5 h-5" />
  }];
  return <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col">
        <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r">
          <div className="flex items-center flex-shrink-0 px-4">
            <span className="text-xl font-semibold text-primary">
              IskedyulKo
            </span>
          </div>
          <div className="mt-5 flex-1 flex flex-col">
            <nav className="flex-1 px-2 pb-4 space-y-1">
              {navItems.map(item => {
              const isActive = location.pathname === item.path;
              return <Link key={item.name} to={item.path} className={`${isActive ? 'bg-gray-100 text-primary' : 'text-gray-600 hover:bg-gray-50 hover:text-primary'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`}>
                    {item.icon}
                    <span className="ml-3">{item.name}</span>
                  </Link>;
            })}
              <button onClick={logout} className="w-full text-left text-gray-600 hover:bg-gray-50 hover:text-primary group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                <LogOut className="w-5 h-5" />
                <span className="ml-3">Logout</span>
              </button>
            </nav>
          </div>
          <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
            <div className="flex items-center">
              <div>
                <div className="bg-gray-300 rounded-full h-9 w-9 flex items-center justify-center text-gray-700 font-semibold">
                  {currentUser?.name.charAt(0)}
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                  {currentUser?.name}
                </p>
                <p className="text-xs font-medium text-gray-500 group-hover:text-gray-700">
                  {currentUser?.businessName}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Mobile header */}
      <div className="md:hidden flex items-center justify-between p-4 bg-white border-b w-full">
        <span className="text-xl font-semibold text-primary">IskedyulKo</span>
        {/* Mobile menu button would go here */}
      </div>
      {/* Main content */}
      <div className="flex flex-col flex-1">
        <main className="flex-1 overflow-y-auto p-6">{children}</main>
      </div>
    </div>;
};
export default DashboardLayout;