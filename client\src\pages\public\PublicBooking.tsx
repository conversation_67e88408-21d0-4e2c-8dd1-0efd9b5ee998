import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Calendar, Clock, ChevronRight } from 'lucide-react';
import { getServicesByBusinessSlug, Service } from '../../api/services';
import { createAppointment, getAvailableTimeSlots } from '../../api/appointments';
import { getBusinessBySlug, Business } from '../../api/business';
const PublicBooking: React.FC = () => {
  const {
    businessSlug
  } = useParams<{
    businessSlug: string;
  }>();
  const [services, setServices] = useState<Service[]>([]);
  const [business, setBusiness] = useState<Business | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  // Booking state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });
  useEffect(() => {
    const fetchData = async () => {
      if (!businessSlug) return;

      try {
        setLoading(true);
        // Fetch business info and services
        const [businessData, servicesData] = await Promise.all([
          getBusinessBySlug(businessSlug),
          getServicesByBusinessSlug(businessSlug)
        ]);

        setBusiness(businessData);
        setServices(servicesData);
      } catch (err) {
        setError('Failed to load business information. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [businessSlug]);
  const handleServiceSelect = (service: Service) => {
    setSelectedService(service);
    setCurrentStep(2);
  };

  const handleDateSelect = async (date: string) => {
    setSelectedDate(date);
    setSelectedTime(''); // Reset selected time

    if (selectedService && businessSlug) {
      try {
        const slotsData = await getAvailableTimeSlots(businessSlug, selectedService.id, date);
        setAvailableSlots(slotsData.availableSlots);
      } catch (err) {
        console.error('Failed to fetch available slots:', err);
        setAvailableSlots([]);
      }
    }

    setCurrentStep(3);
  };
  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setCurrentStep(4);
  };
  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {
      name,
      value
    } = e.target;
    setCustomerInfo({
      ...customerInfo,
      [name]: value
    });
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedService || !selectedDate || !selectedTime || !businessSlug) {
      setError('Please complete all booking steps');
      return;
    }
    try {
      await createAppointment(businessSlug, {
        customerName: customerInfo.name,
        customerEmail: customerInfo.email,
        customerPhone: customerInfo.phone,
        serviceId: selectedService.id,
        appointmentDate: selectedDate,
        appointmentTime: selectedTime
      });
      setSuccess(true);
    } catch (err) {
      setError('Failed to create booking. Please try again.');
    }
  };
  // Generate available dates (next 7 days)
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      // Skip Sundays (you can enhance this with actual business hours)
      if (date.getDay() === 0) {
        continue;
      }
      dates.push({
        dateString: date.toISOString().split('T')[0],
        display: date.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        })
      });
    }
    return dates;
  };
  // Format time for display
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };
  if (success) {
    return <div className="min-h-screen bg-gray-50 flex flex-col items-center pt-12 px-4">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="mt-3 text-lg font-medium text-gray-900">
              Booking Successful!
            </h2>
            <p className="mt-2 text-sm text-gray-500">
              Your appointment has been scheduled for{' '}
              {new Date(selectedDate).toLocaleDateString('en-US', {
              weekday: 'long',
              month: 'long',
              day: 'numeric'
            })}{' '}
              at {selectedTime}.
            </p>
            <p className="mt-1 text-sm text-gray-500">
              We've sent a confirmation email to {customerInfo.email}.
            </p>
            <div className="mt-6">
              <button onClick={() => window.location.reload()} className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-focus">
                Book Another Appointment
              </button>
            </div>
          </div>
        </div>
      </div>;
  }
  return <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900">
            {business?.name || 'Business'}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {business?.description || 'Professional services at affordable prices.'}
          </p>
        </div>
      </header>
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Booking Steps */}
            <div className="w-full md:w-2/3">
              <div className="bg-white shadow rounded-lg overflow-hidden">
                {/* Stepper */}
                <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
                  <div className="flex items-center space-x-2">
                    <div className={`flex items-center justify-center h-8 w-8 rounded-full ${currentStep >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'}`}>
                      1
                    </div>
                    <div className="h-0.5 w-8 bg-gray-200"></div>
                    <div className={`flex items-center justify-center h-8 w-8 rounded-full ${currentStep >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'}`}>
                      2
                    </div>
                    <div className="h-0.5 w-8 bg-gray-200"></div>
                    <div className={`flex items-center justify-center h-8 w-8 rounded-full ${currentStep >= 3 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'}`}>
                      3
                    </div>
                    <div className="h-0.5 w-8 bg-gray-200"></div>
                    <div className={`flex items-center justify-center h-8 w-8 rounded-full ${currentStep >= 4 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'}`}>
                      4
                    </div>
                  </div>
                  <div className="flex justify-between text-xs mt-2">
                    <span>Service</span>
                    <span>Date</span>
                    <span>Time</span>
                    <span>Your Info</span>
                  </div>
                </div>
                {/* Step Content */}
                <div className="p-6">
                  {currentStep === 1 && <div>
                      <h2 className="text-lg font-medium text-gray-900">
                        Select a Service
                      </h2>
                      {loading ? <div className="py-4">Loading services...</div> : error ? <div className="py-4 text-red-500">{error}</div> : <div className="mt-4 space-y-4">
                          {services.map(service => <div key={service.id} className="border rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-gray-50" onClick={() => handleServiceSelect(service)}>
                              <div className="flex justify-between items-center">
                                <div>
                                  <h3 className="text-sm font-medium text-gray-900">
                                    {service.name}
                                  </h3>
                                  <p className="text-sm text-gray-500">
                                    {service.durationMinutes} minutes
                                  </p>
                                </div>
                                <div className="flex items-center">
                                  <span className="text-lg font-medium text-gray-900 mr-2">
                                    ₱{service.price.toFixed(2)}
                                  </span>
                                  <ChevronRight className="h-5 w-5 text-gray-400" />
                                </div>
                              </div>
                            </div>)}
                        </div>}
                    </div>}
                  {currentStep === 2 && <div>
                      <div className="flex items-center mb-4">
                        <button onClick={() => setCurrentStep(1)} className="mr-2 text-gray-500 hover:text-gray-700">
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>
                        <h2 className="text-lg font-medium text-gray-900">
                          Select a Date
                        </h2>
                      </div>
                      <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-4">
                        {getAvailableDates().map(date => <div key={date.dateString} className={`border rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-gray-50 flex items-center justify-center ${selectedDate === date.dateString ? 'border-primary bg-primary bg-opacity-10' : ''}`} onClick={() => handleDateSelect(date.dateString)}>
                            <div className="text-center">
                              <Calendar className="h-5 w-5 mx-auto text-gray-500" />
                              <div className="mt-1 text-sm font-medium">
                                {date.display}
                              </div>
                            </div>
                          </div>)}
                      </div>
                    </div>}
                  {currentStep === 3 && <div>
                      <div className="flex items-center mb-4">
                        <button onClick={() => setCurrentStep(2)} className="mr-2 text-gray-500 hover:text-gray-700">
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>
                        <h2 className="text-lg font-medium text-gray-900">
                          Select a Time
                        </h2>
                      </div>
                      <p className="text-sm text-gray-500 mb-4">
                        Available time slots for{' '}
                        {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      month: 'long',
                      day: 'numeric'
                    })}
                      </p>
                      <div className="mt-4 grid grid-cols-3 sm:grid-cols-4 gap-3">
                        {availableSlots.length > 0 ? availableSlots.map(time => <div key={time} className={`border rounded-lg p-3 cursor-pointer hover:border-primary hover:bg-gray-50 flex items-center justify-center ${selectedTime === time ? 'border-primary bg-primary bg-opacity-10' : ''}`} onClick={() => handleTimeSelect(time)}>
                            <div className="text-center">
                              <Clock className="h-4 w-4 mx-auto text-gray-500" />
                              <div className="mt-1 text-sm font-medium">
                                {formatTime(time)}
                              </div>
                            </div>
                          </div>) : <div className="col-span-full text-center text-gray-500 py-4">
                            No available time slots for this date
                          </div>}
                      </div>
                    </div>}
                  {currentStep === 4 && <div>
                      <div className="flex items-center mb-4">
                        <button onClick={() => setCurrentStep(3)} className="mr-2 text-gray-500 hover:text-gray-700">
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>
                        <h2 className="text-lg font-medium text-gray-900">
                          Your Information
                        </h2>
                      </div>
                      {error && <div className="mb-4 bg-red-50 p-3 rounded text-red-600 text-sm">
                          {error}
                        </div>}
                      <form onSubmit={handleSubmit}>
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                              Full Name
                            </label>
                            <input type="text" name="name" id="name" required value={customerInfo.name} onChange={handleCustomerInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                          </div>
                          <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                              Email Address
                            </label>
                            <input type="email" name="email" id="email" required value={customerInfo.email} onChange={handleCustomerInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                          </div>
                          <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                              Phone Number
                            </label>
                            <input type="tel" name="phone" id="phone" value={customerInfo.phone} onChange={handleCustomerInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                          </div>
                          <div className="pt-4">
                            <button type="submit" className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-focus focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                              Confirm Booking
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>}
                </div>
              </div>
            </div>
            {/* Booking Summary */}
            <div className="w-full md:w-1/3">
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Booking Summary
                </h2>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Service:</span>
                    <span className="text-sm font-medium">
                      {selectedService?.name || 'Not selected'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Date:</span>
                    <span className="text-sm font-medium">
                      {selectedDate ? new Date(selectedDate).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    }) : 'Not selected'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Time:</span>
                    <span className="text-sm font-medium">
                      {selectedTime || 'Not selected'}
                    </span>
                  </div>
                  {selectedService && <>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Duration:</span>
                        <span className="text-sm font-medium">
                          {selectedService.durationMinutes} minutes
                        </span>
                      </div>
                      <div className="flex justify-between pt-4 border-t">
                        <span className="text-base font-medium">Total:</span>
                        <span className="text-base font-bold">
                          ₱{selectedService.price.toFixed(2)}
                        </span>
                      </div>
                    </>}
                </div>
              </div>
              {/* Business Info */}
              <div className="bg-white shadow rounded-lg p-6 mt-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Business Information
                </h2>
                <div className="space-y-3">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      Address
                    </h3>
                    <p className="text-sm text-gray-500">
                      {business?.address || '123 Main Street, Manila, Philippines'}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      Working Hours
                    </h3>
                    <p className="text-sm text-gray-500">
                      9:00 AM - 6:00 PM
                    </p>
                    <p className="text-sm text-gray-500">
                      Monday to Saturday
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      Contact
                    </h3>
                    <p className="text-sm text-gray-500">
                      {business?.phone || '+63 ************'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>;
};
export default PublicBooking;