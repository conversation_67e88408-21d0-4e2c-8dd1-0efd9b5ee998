// API configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get auth token
const getAuthToken = (): string | null => {
  return localStorage.getItem('token');
};

// Helper function to create headers with auth token
const createHeaders = (includeAuth: boolean = true): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  return headers;
};

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ 
      error: 'Network error', 
      message: 'Failed to connect to server' 
    }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }
  return response.json();
};

// Generic API request function
export const apiRequest = async (
  endpoint: string, 
  options: RequestInit = {}, 
  includeAuth: boolean = true
) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const headers = createHeaders(includeAuth);

  const config: RequestInit = {
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    return await handleResponse(response);
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Specific HTTP method helpers
export const api = {
  get: (endpoint: string, includeAuth: boolean = true) => 
    apiRequest(endpoint, { method: 'GET' }, includeAuth),
  
  post: (endpoint: string, data: any, includeAuth: boolean = true) => 
    apiRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    }, includeAuth),
  
  put: (endpoint: string, data: any, includeAuth: boolean = true) => 
    apiRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, includeAuth),
  
  patch: (endpoint: string, data: any, includeAuth: boolean = true) => 
    apiRequest(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    }, includeAuth),
  
  delete: (endpoint: string, includeAuth: boolean = true) => 
    apiRequest(endpoint, { method: 'DELETE' }, includeAuth),
};

export { API_BASE_URL };
