const express = require('express');
const { body, param } = require('express-validator');
const { executeQuery } = require('../config/database');
const { handleValidationErrors } = require('../middleware/errorHandler');
const { authenticateToken, requireBusiness } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const serviceValidation = [
  body('name').trim().isLength({ min: 1, max: 255 }).withMessage('Service name is required and must be less than 255 characters'),
  body('description').optional().trim().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('durationMinutes').isInt({ min: 1, max: 480 }).withMessage('Duration must be between 1 and 480 minutes')
];

const serviceIdValidation = [
  param('id').isInt({ min: 1 }).withMessage('Invalid service ID')
];

// Get all services for a business (public endpoint for booking page)
router.get('/business/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    const services = await executeQuery(`
      SELECT s.id, s.name, s.description, s.price, s.duration_minutes as durationMinutes
      FROM services s
      JOIN businesses b ON s.business_id = b.id
      WHERE b.slug = ? AND s.is_active = 1
      ORDER BY s.display_order ASC, s.name ASC
    `, [slug]);

    res.json(services);

  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({
      error: 'Failed to fetch services',
      message: 'An error occurred while fetching services'
    });
  }
});

// Get all services for authenticated business owner
router.get('/', authenticateToken, requireBusiness, async (req, res) => {
  try {
    const services = await executeQuery(`
      SELECT id, name, description, price, duration_minutes as durationMinutes, 
             is_active as isActive, display_order as displayOrder,
             created_at as createdAt, updated_at as updatedAt
      FROM services 
      WHERE business_id = ?
      ORDER BY display_order ASC, name ASC
    `, [req.user.businessId]);

    res.json(services);

  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({
      error: 'Failed to fetch services',
      message: 'An error occurred while fetching services'
    });
  }
});

// Get single service
router.get('/:id', serviceIdValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;

    const services = await executeQuery(`
      SELECT id, name, description, price, duration_minutes as durationMinutes, 
             is_active as isActive, display_order as displayOrder,
             created_at as createdAt, updated_at as updatedAt
      FROM services 
      WHERE id = ? AND business_id = ?
    `, [id, req.user.businessId]);

    if (services.length === 0) {
      return res.status(404).json({
        error: 'Service not found',
        message: 'The requested service does not exist'
      });
    }

    res.json(services[0]);

  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({
      error: 'Failed to fetch service',
      message: 'An error occurred while fetching the service'
    });
  }
});

// Create new service
router.post('/', serviceValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { name, description, price, durationMinutes } = req.body;

    // Get the next display order
    const maxOrderResult = await executeQuery(
      'SELECT COALESCE(MAX(display_order), 0) as maxOrder FROM services WHERE business_id = ?',
      [req.user.businessId]
    );
    const displayOrder = maxOrderResult[0].maxOrder + 1;

    const result = await executeQuery(`
      INSERT INTO services (business_id, name, description, price, duration_minutes, display_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [req.user.businessId, name, description || null, price, durationMinutes, displayOrder]);

    // Get the created service
    const newService = await executeQuery(`
      SELECT id, name, description, price, duration_minutes as durationMinutes, 
             is_active as isActive, display_order as displayOrder,
             created_at as createdAt, updated_at as updatedAt
      FROM services 
      WHERE id = ?
    `, [result.insertId]);

    res.status(201).json({
      message: 'Service created successfully',
      service: newService[0]
    });

  } catch (error) {
    console.error('Create service error:', error);
    res.status(500).json({
      error: 'Failed to create service',
      message: 'An error occurred while creating the service'
    });
  }
});

// Update service
router.put('/:id', serviceIdValidation, serviceValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, price, durationMinutes } = req.body;

    // Check if service exists and belongs to user's business
    const existingService = await executeQuery(
      'SELECT id FROM services WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    if (existingService.length === 0) {
      return res.status(404).json({
        error: 'Service not found',
        message: 'The requested service does not exist'
      });
    }

    await executeQuery(`
      UPDATE services 
      SET name = ?, description = ?, price = ?, duration_minutes = ?
      WHERE id = ? AND business_id = ?
    `, [name, description || null, price, durationMinutes, id, req.user.businessId]);

    // Get the updated service
    const updatedService = await executeQuery(`
      SELECT id, name, description, price, duration_minutes as durationMinutes, 
             is_active as isActive, display_order as displayOrder,
             created_at as createdAt, updated_at as updatedAt
      FROM services 
      WHERE id = ?
    `, [id]);

    res.json({
      message: 'Service updated successfully',
      service: updatedService[0]
    });

  } catch (error) {
    console.error('Update service error:', error);
    res.status(500).json({
      error: 'Failed to update service',
      message: 'An error occurred while updating the service'
    });
  }
});

// Toggle service active status
router.patch('/:id/toggle', serviceIdValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if service exists and belongs to user's business
    const existingService = await executeQuery(
      'SELECT id, is_active FROM services WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    if (existingService.length === 0) {
      return res.status(404).json({
        error: 'Service not found',
        message: 'The requested service does not exist'
      });
    }

    const newStatus = !existingService[0].is_active;

    await executeQuery(
      'UPDATE services SET is_active = ? WHERE id = ? AND business_id = ?',
      [newStatus, id, req.user.businessId]
    );

    res.json({
      message: `Service ${newStatus ? 'activated' : 'deactivated'} successfully`,
      isActive: newStatus
    });

  } catch (error) {
    console.error('Toggle service error:', error);
    res.status(500).json({
      error: 'Failed to toggle service status',
      message: 'An error occurred while updating the service status'
    });
  }
});

// Delete service
router.delete('/:id', serviceIdValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if service exists and belongs to user's business
    const existingService = await executeQuery(
      'SELECT id FROM services WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    if (existingService.length === 0) {
      return res.status(404).json({
        error: 'Service not found',
        message: 'The requested service does not exist'
      });
    }

    // Check if service has any appointments
    const appointments = await executeQuery(
      'SELECT id FROM appointments WHERE service_id = ? LIMIT 1',
      [id]
    );

    if (appointments.length > 0) {
      return res.status(400).json({
        error: 'Cannot delete service',
        message: 'This service has existing appointments and cannot be deleted. You can deactivate it instead.'
      });
    }

    await executeQuery(
      'DELETE FROM services WHERE id = ? AND business_id = ?',
      [id, req.user.businessId]
    );

    res.json({
      message: 'Service deleted successfully'
    });

  } catch (error) {
    console.error('Delete service error:', error);
    res.status(500).json({
      error: 'Failed to delete service',
      message: 'An error occurred while deleting the service'
    });
  }
});

module.exports = router;
