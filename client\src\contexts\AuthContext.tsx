import React, { useEffect, useState, createContext, useContext } from 'react';
import { loginUser, registerUser, getCurrentUser } from '../api/auth';
interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}
interface User {
  id: number;
  name: string;
  email: string;
  businessName: string;
  slug: string;
}
interface RegisterData {
  name: string;
  email: string;
  password: string;
  businessName: string;
}
const AuthContext = createContext<AuthContextType | undefined>(undefined);
export const AuthProvider: React.FC<{
  children: React.ReactNode;
}> = ({
  children
}) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          const user = await getCurrentUser();
          setCurrentUser(user);
        }
      } catch (error) {
        localStorage.removeItem('token');
      } finally {
        setLoading(false);
      }
    };
    initAuth();
  }, []);
  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const {
        user,
        token
      } = await loginUser(email, password);
      localStorage.setItem('token', token);
      setCurrentUser(user);
    } finally {
      setLoading(false);
    }
  };
  const register = async (userData: RegisterData) => {
    setLoading(true);
    try {
      const {
        user,
        token
      } = await registerUser(userData);
      localStorage.setItem('token', token);
      setCurrentUser(user);
    } finally {
      setLoading(false);
    }
  };
  const logout = () => {
    localStorage.removeItem('token');
    setCurrentUser(null);
  };
  return <AuthContext.Provider value={{
    currentUser,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!currentUser
  }}>
      {children}
    </AuthContext.Provider>;
};
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};