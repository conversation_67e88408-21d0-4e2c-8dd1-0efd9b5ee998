import React, { useState } from 'react';
import DashboardLayout from '../../components/layout/DashboardLayout';
import { useAuth } from '../../contexts/AuthContext';
const Settings: React.FC = () => {
  const {
    currentUser
  } = useAuth();
  const [businessInfo, setBusinessInfo] = useState({
    name: currentUser?.businessName || '',
    address: '123 Main Street, Makati City',
    phone: '+63 ************',
    email: currentUser?.email || '',
    description: 'Professional services at affordable prices.'
  });
  const [workingHours, setWorkingHours] = useState([{
    day: 'Monday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '18:00'
  }, {
    day: 'Tuesday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '18:00'
  }, {
    day: 'Wednesday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '18:00'
  }, {
    day: 'Thursday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '18:00'
  }, {
    day: 'Friday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '18:00'
  }, {
    day: 'Saturday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '18:00'
  }, {
    day: 'Sunday',
    isOpen: false,
    openTime: '09:00',
    closeTime: '18:00'
  }]);
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [savedMessage, setSavedMessage] = useState('');
  const handleBusinessInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const {
      name,
      value
    } = e.target;
    setBusinessInfo({
      ...businessInfo,
      [name]: value
    });
  };
  const handleWorkingHoursChange = (index: number, field: string, value: any) => {
    const updatedHours = [...workingHours];
    updatedHours[index] = {
      ...updatedHours[index],
      [field]: value
    };
    setWorkingHours(updatedHours);
  };
  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    setSavedMessage('Settings saved successfully');
    setIsSaving(false);
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSavedMessage('');
    }, 3000);
  };
  return <DashboardLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px">
              <button onClick={() => setActiveTab('general')} className={`py-4 px-6 text-sm font-medium ${activeTab === 'general' ? 'border-b-2 border-primary text-primary' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                General
              </button>
              <button onClick={() => setActiveTab('hours')} className={`py-4 px-6 text-sm font-medium ${activeTab === 'hours' ? 'border-b-2 border-primary text-primary' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                Working Hours
              </button>
              <button onClick={() => setActiveTab('account')} className={`py-4 px-6 text-sm font-medium ${activeTab === 'account' ? 'border-b-2 border-primary text-primary' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                Account
              </button>
            </nav>
          </div>
          {/* Tab Content */}
          <div className="p-6">
            {savedMessage && <div className="mb-4 bg-green-50 p-3 rounded text-green-700 text-sm">
                {savedMessage}
              </div>}
            <form onSubmit={handleSaveSettings}>
              {/* General Settings */}
              {activeTab === 'general' && <div className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Business Name
                    </label>
                    <input type="text" name="name" id="name" value={businessInfo.name} onChange={handleBusinessInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                  </div>
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Business Description
                    </label>
                    <textarea name="description" id="description" rows={3} value={businessInfo.description} onChange={handleBusinessInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                  </div>
                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                      Address
                    </label>
                    <input type="text" name="address" id="address" value={businessInfo.address} onChange={handleBusinessInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                  </div>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                        Phone Number
                      </label>
                      <input type="text" name="phone" id="phone" value={businessInfo.phone} onChange={handleBusinessInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address
                      </label>
                      <input type="email" name="email" id="email" value={businessInfo.email} onChange={handleBusinessInfoChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                    </div>
                  </div>
                </div>}
              {/* Working Hours */}
              {activeTab === 'hours' && <div className="space-y-6">
                  <div className="border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Day
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Opening Time
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Closing Time
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {workingHours.map((day, index) => <tr key={day.day}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {day.day}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex items-center">
                                <input type="checkbox" checked={day.isOpen} onChange={e => handleWorkingHoursChange(index, 'isOpen', e.target.checked)} className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" />
                                <span className="ml-2">
                                  {day.isOpen ? 'Open' : 'Closed'}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <input type="time" value={day.openTime} onChange={e => handleWorkingHoursChange(index, 'openTime', e.target.value)} disabled={!day.isOpen} className="border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-primary focus:border-primary" />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <input type="time" value={day.closeTime} onChange={e => handleWorkingHoursChange(index, 'closeTime', e.target.value)} disabled={!day.isOpen} className="border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-primary focus:border-primary" />
                            </td>
                          </tr>)}
                      </tbody>
                    </table>
                  </div>
                </div>}
              {/* Account Settings */}
              {activeTab === 'account' && <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      Change Password
                    </h3>
                    <div className="mt-4 space-y-4">
                      <div>
                        <label htmlFor="current-password" className="block text-sm font-medium text-gray-700">
                          Current Password
                        </label>
                        <input type="password" name="current-password" id="current-password" className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                      </div>
                      <div>
                        <label htmlFor="new-password" className="block text-sm font-medium text-gray-700">
                          New Password
                        </label>
                        <input type="password" name="new-password" id="new-password" className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                      </div>
                      <div>
                        <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700">
                          Confirm New Password
                        </label>
                        <input type="password" name="confirm-password" id="confirm-password" className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" />
                      </div>
                    </div>
                  </div>
                  <div className="pt-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Danger Zone
                    </h3>
                    <div className="mt-4">
                      <button type="button" className="px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 hover:bg-red-50">
                        Delete Account
                      </button>
                    </div>
                  </div>
                </div>}
              <div className="mt-8 flex justify-end">
                <button type="submit" disabled={isSaving} className="px-4 py-2 bg-primary border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-focus focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>;
};
export default Settings;