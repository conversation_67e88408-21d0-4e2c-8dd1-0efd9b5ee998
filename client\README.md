# IskedyulKo Frontend

React frontend for the IskedyulKo appointment booking system.

## Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
Copy `.env.example` to `.env` and configure:

```env
# API Configuration
VITE_API_URL=http://localhost:5000/api
```

### 3. Start Development Server
```bash
npm run dev
```

The application will start on `http://localhost:5173`

## Features

### Public Pages
- **Landing Page** (`/`) - Business showcase and information
- **Public Booking** (`/book/:businessSlug`) - Customer appointment booking
- **Authentication** (`/login`, `/register`) - User login and registration

### Dashboard (Authenticated)
- **Dashboard** (`/dashboard`) - Overview and statistics
- **Services** (`/dashboard/services`) - Manage business services
- **Appointments** (`/dashboard/appointments`) - View and manage appointments
- **Settings** (`/dashboard/settings`) - Business profile settings

## API Integration

The app connects to the Express.js backend API. Make sure the backend is running on `http://localhost:5000` before starting the frontend.

### Default Login Credentials
- **Email**: `<EMAIL>`
- **Password**: `password123`

## Development

### Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Tech Stack

- React 18 with TypeScript
- Vite for development
- Tailwind CSS for styling
- React Router for navigation
- Lucide React for icons