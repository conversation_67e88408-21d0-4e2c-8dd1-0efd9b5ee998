# IskedyulKo Backend API

Express.js backend for the IskedyulKo appointment booking system.

## Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
Copy `.env.example` to `.env` and configure:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=iskedyulko_db
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
CLIENT_URL=http://localhost:5173
```

### 3. Database Setup
1. Create MySQL database using the SQL file in `database/iskedyulko_database.sql`
2. Import the file into phpMyAdmin or run it via MySQL command line
3. This creates the database with sample data

### 4. Start Development Server
```bash
npm run dev
```

The server will start on `http://localhost:5000`

## API Documentation

### Health Check
- `GET /health` - Server health status

### Authentication Routes (`/api/auth`)
- `POST /register` - Register new business owner
- `POST /login` - User login
- `GET /me` - Get current user (requires auth)
- `POST /logout` - User logout (requires auth)

### Services Routes (`/api/services`)
- `GET /` - Get all services for authenticated business
- `GET /business/:slug` - Get services by business slug (public)
- `GET /:id` - Get single service (requires auth)
- `POST /` - Create new service (requires auth)
- `PUT /:id` - Update service (requires auth)
- `PATCH /:id/toggle` - Toggle service active status (requires auth)
- `DELETE /:id` - Delete service (requires auth)

### Appointments Routes (`/api/appointments`)
- `GET /` - Get appointments with filters (requires auth)
- `GET /:id` - Get single appointment (requires auth)
- `POST /book/:businessSlug` - Book appointment (public)
- `PATCH /:id/status` - Update appointment status (requires auth)
- `PATCH /:id/notes` - Update appointment notes (requires auth)
- `DELETE /:id` - Delete appointment (requires auth)
- `GET /availability/:businessSlug/:serviceId/:date` - Get available time slots (public)

### Business Routes (`/api/business`)
- `GET /profile` - Get business profile (requires auth)
- `PUT /profile` - Update business profile (requires auth)
- `GET /stats` - Get dashboard statistics (requires auth)
- `GET /:slug` - Get business by slug (public)

## Database Schema

### Tables
- `businesses` - Business information
- `users` - User accounts (business owners)
- `services` - Services offered by businesses
- `appointments` - Customer appointments
- `business_settings` - Business configuration settings

### Sample Data
The database includes sample data:
- Business: "Juan's Barbershop"
- User: <EMAIL> (password: password123)
- Services: Haircut, Beard Trim, Hair Coloring, Shave
- Sample appointments

## Security Features

- JWT authentication
- Password hashing with bcryptjs
- Input validation with express-validator
- Rate limiting
- CORS protection
- Helmet security headers

## Error Handling

The API returns consistent error responses:
```json
{
  "error": "Error type",
  "message": "Human readable message",
  "details": [] // Optional validation details
}
```

## Development

### Scripts
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run tests (when implemented)

### Adding New Routes
1. Create route file in `src/routes/`
2. Add route to `src/index.js`
3. Use middleware for authentication and validation

### Database Queries
Use the `executeQuery` helper from `src/config/database.js` for consistent error handling.

## Deployment

1. Set `NODE_ENV=production` in environment
2. Update database credentials
3. Change JWT_SECRET to a secure random string
4. Configure CORS for production domain
5. Use process manager like PM2 for production
