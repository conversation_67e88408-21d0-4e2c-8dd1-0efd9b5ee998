import { api } from './config';

export interface Business {
  id: number;
  name: string;
  slug: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  businessHours?: any;
  timezone?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface BusinessStats {
  overview: {
    total_services: number;
    total_appointments: number;
    confirmed_appointments: number;
    pending_appointments: number;
    completed_appointments: number;
    today_appointments: number;
    today_confirmed: number;
    total_revenue: number;
    monthly_revenue: number;
  };
  recentAppointments: Array<{
    id: number;
    customerName: string;
    appointmentDate: string;
    appointmentTime: string;
    status: string;
    serviceName: string;
    price: number;
  }>;
  upcomingAppointments: Array<{
    id: number;
    customerName: string;
    appointmentDate: string;
    appointmentTime: string;
    status: string;
    serviceName: string;
    price: number;
  }>;
  monthlyData: Array<{
    month: string;
    appointment_count: number;
    revenue: number;
  }>;
  serviceStats: Array<{
    serviceName: string;
    booking_count: number;
    revenue: number;
  }>;
}

export interface UpdateBusinessData {
  name?: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  businessHours?: any;
}

// Get business profile
export const getBusinessProfile = async (): Promise<Business> => {
  const response = await api.get('/business/profile');
  return response;
};

// Update business profile
export const updateBusinessProfile = async (businessData: UpdateBusinessData): Promise<Business> => {
  const response = await api.put('/business/profile', businessData);
  return response.business;
};

// Get dashboard statistics
export const getDashboardStats = async (): Promise<BusinessStats> => {
  const response = await api.get('/business/stats');
  return response;
};

// Get business by slug (public)
export const getBusinessBySlug = async (slug: string): Promise<Business> => {
  const response = await api.get(`/business/${slug}`, false);
  return response;
};
