import React, { useEffect, useState } from 'react';
import { Check, X, Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import DashboardLayout from '../../components/layout/DashboardLayout';
import { getAppointments, updateAppointmentStatus, Appointment } from '../../api/appointments';
const AppointmentsPage: React.FC = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');
  const [currentDate, setCurrentDate] = useState(new Date());
  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        const data = await getAppointments();
        setAppointments(data);
      } catch (err) {
        setError('Failed to load appointments');
      } finally {
        setLoading(false);
      }
    };
    fetchAppointments();
  }, []);
  const handleStatusChange = async (id: number, status: 'confirmed' | 'cancelled') => {
    try {
      await updateAppointmentStatus(id, status);
      // Update the appointment in the local state
      setAppointments(appointments.map(app =>
        app.id === id ? { ...app, status } : app
      ));
    } catch (err) {
      setError('Failed to update appointment status');
    }
  };
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Confirmed
          </span>;
      case 'cancelled':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
            Cancelled
          </span>;
      default:
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
            Pending
          </span>;
    }
  };
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };
  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    const days = [];
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-24 border border-gray-200 bg-gray-50"></div>);
    }
    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const dayAppointments = appointments.filter(app => app.appointmentDate === date);
      days.push(<div key={day} className="h-24 border border-gray-200 p-1">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium">{day}</span>
            {dayAppointments.length > 0 && <span className="text-xs bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center">
                {dayAppointments.length}
              </span>}
          </div>
          <div className="overflow-y-auto max-h-16">
            {dayAppointments.map(app => <div key={app.id} className={`text-xs p-1 mb-1 rounded truncate ${app.status === 'confirmed' ? 'bg-green-100' : app.status === 'cancelled' ? 'bg-red-100' : 'bg-yellow-100'}`}>
                {app.appointmentTime} - {app.customerName}
              </div>)}
          </div>
        </div>);
    }
    return days;
  };
  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };
  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };
  return <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Appointments</h1>
          <div className="flex space-x-2">
            <button onClick={() => setViewMode('list')} className={`px-4 py-2 rounded-md ${viewMode === 'list' ? 'bg-primary text-white' : 'bg-white text-gray-700 border border-gray-300'}`}>
              List
            </button>
            <button onClick={() => setViewMode('calendar')} className={`px-4 py-2 rounded-md ${viewMode === 'calendar' ? 'bg-primary text-white' : 'bg-white text-gray-700 border border-gray-300'}`}>
              Calendar
            </button>
          </div>
        </div>
        {error && <div className="bg-red-50 p-4 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <X className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>}
        {viewMode === 'list' ? <div className="bg-white shadow rounded-lg overflow-hidden">
            {loading ? <div className="p-4 text-center">Loading appointments...</div> : appointments.length > 0 ? <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date & Time
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {appointments.map(appointment => <tr key={appointment.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {appointment.customerName.charAt(0)}
                              </span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {appointment.customerName}
                              </div>
                              <div className="text-sm text-gray-500">
                                {appointment.customerEmail}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {appointment.serviceName}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {new Date(appointment.appointmentDate).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                          </div>
                          <div className="text-sm text-gray-500">
                            {appointment.appointmentTime}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(appointment.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          {appointment.status === 'pending' && <div className="flex justify-end space-x-2">
                              <button onClick={() => handleStatusChange(appointment.id, 'confirmed')} className="text-green-600 hover:text-green-900">
                                <Check className="h-5 w-5" />
                              </button>
                              <button onClick={() => handleStatusChange(appointment.id, 'cancelled')} className="text-red-600 hover:text-red-900">
                                <X className="h-5 w-5" />
                              </button>
                            </div>}
                        </td>
                      </tr>)}
                  </tbody>
                </table>
              </div> : <div className="p-4 text-center text-gray-500">
                No appointments found
              </div>}
          </div> : <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="p-4 flex items-center justify-between border-b">
              <button onClick={prevMonth} className="p-1 rounded-full hover:bg-gray-100">
                <ChevronLeft className="h-5 w-5" />
              </button>
              <h2 className="text-lg font-medium text-gray-900">
                {currentDate.toLocaleDateString('en-US', {
              month: 'long',
              year: 'numeric'
            })}
              </h2>
              <button onClick={nextMonth} className="p-1 rounded-full hover:bg-gray-100">
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
            <div className="grid grid-cols-7 gap-px">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => <div key={day} className="text-center py-2 bg-gray-50 font-medium text-sm">
                  {day}
                </div>)}
              {renderCalendar()}
            </div>
          </div>}
      </div>
    </DashboardLayout>;
};
export default AppointmentsPage;