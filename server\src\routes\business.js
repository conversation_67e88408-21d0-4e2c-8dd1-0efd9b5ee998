const express = require('express');
const { body } = require('express-validator');
const { executeQuery } = require('../config/database');
const { handleValidationErrors } = require('../middleware/errorHandler');
const { authenticateToken, requireBusiness } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const businessUpdateValidation = [
  body('name').optional().trim().isLength({ min: 1, max: 255 }).withMessage('Business name must be between 1 and 255 characters'),
  body('description').optional().trim().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('address').optional().trim().isLength({ max: 500 }).withMessage('Address must be less than 500 characters'),
  body('phone').optional().trim().isLength({ max: 20 }).withMessage('Phone must be less than 20 characters'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('website').optional().trim().isURL().withMessage('Please provide a valid website URL')
];

// Get business profile
router.get('/profile', authenticateToken, requireBusiness, async (req, res) => {
  try {
    const business = await executeQuery(`
      SELECT id, name, slug, description, address, phone, email, website, 
             business_hours as businessHours, timezone, is_active as isActive,
             created_at as createdAt, updated_at as updatedAt
      FROM businesses 
      WHERE id = ?
    `, [req.user.businessId]);

    if (business.length === 0) {
      return res.status(404).json({
        error: 'Business not found',
        message: 'Business profile not found'
      });
    }

    // Parse business hours JSON
    const businessData = business[0];
    if (businessData.businessHours) {
      try {
        businessData.businessHours = JSON.parse(businessData.businessHours);
      } catch (e) {
        businessData.businessHours = null;
      }
    }

    res.json(businessData);

  } catch (error) {
    console.error('Get business profile error:', error);
    res.status(500).json({
      error: 'Failed to fetch business profile',
      message: 'An error occurred while fetching the business profile'
    });
  }
});

// Update business profile
router.put('/profile', businessUpdateValidation, handleValidationErrors, authenticateToken, requireBusiness, async (req, res) => {
  try {
    const { name, description, address, phone, email, website, businessHours } = req.body;

    // Build update query dynamically based on provided fields
    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    if (address !== undefined) {
      updateFields.push('address = ?');
      updateValues.push(address);
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (website !== undefined) {
      updateFields.push('website = ?');
      updateValues.push(website);
    }
    if (businessHours !== undefined) {
      updateFields.push('business_hours = ?');
      updateValues.push(JSON.stringify(businessHours));
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        error: 'No fields to update',
        message: 'Please provide at least one field to update'
      });
    }

    updateValues.push(req.user.businessId);

    await executeQuery(
      `UPDATE businesses SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // Get updated business profile
    const updatedBusiness = await executeQuery(`
      SELECT id, name, slug, description, address, phone, email, website, 
             business_hours as businessHours, timezone, is_active as isActive,
             created_at as createdAt, updated_at as updatedAt
      FROM businesses 
      WHERE id = ?
    `, [req.user.businessId]);

    const businessData = updatedBusiness[0];
    if (businessData.businessHours) {
      try {
        businessData.businessHours = JSON.parse(businessData.businessHours);
      } catch (e) {
        businessData.businessHours = null;
      }
    }

    res.json({
      message: 'Business profile updated successfully',
      business: businessData
    });

  } catch (error) {
    console.error('Update business profile error:', error);
    res.status(500).json({
      error: 'Failed to update business profile',
      message: 'An error occurred while updating the business profile'
    });
  }
});

// Get dashboard statistics
router.get('/stats', authenticateToken, requireBusiness, async (req, res) => {
  try {
    // Get basic stats
    const stats = await executeQuery(`
      SELECT 
        COUNT(DISTINCT s.id) as total_services,
        COUNT(DISTINCT a.id) as total_appointments,
        COUNT(DISTINCT CASE WHEN a.status = 'confirmed' THEN a.id END) as confirmed_appointments,
        COUNT(DISTINCT CASE WHEN a.status = 'pending' THEN a.id END) as pending_appointments,
        COUNT(DISTINCT CASE WHEN a.status = 'completed' THEN a.id END) as completed_appointments,
        COUNT(DISTINCT CASE WHEN a.appointment_date = CURDATE() THEN a.id END) as today_appointments,
        COUNT(DISTINCT CASE WHEN a.appointment_date = CURDATE() AND a.status = 'confirmed' THEN a.id END) as today_confirmed,
        COALESCE(SUM(CASE WHEN a.status IN ('confirmed', 'completed') THEN s.price END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN a.appointment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND a.status IN ('confirmed', 'completed') THEN s.price END), 0) as monthly_revenue
      FROM businesses b
      LEFT JOIN services s ON b.id = s.business_id AND s.is_active = 1
      LEFT JOIN appointments a ON b.id = a.business_id
      WHERE b.id = ?
      GROUP BY b.id
    `, [req.user.businessId]);

    // Get recent appointments
    const recentAppointments = await executeQuery(`
      SELECT a.id, a.customer_name as customerName, a.appointment_date as appointmentDate,
             a.appointment_time as appointmentTime, a.status,
             s.name as serviceName, s.price
      FROM appointments a
      JOIN services s ON a.service_id = s.id
      WHERE a.business_id = ?
      ORDER BY a.created_at DESC
      LIMIT 5
    `, [req.user.businessId]);

    // Get upcoming appointments
    const upcomingAppointments = await executeQuery(`
      SELECT a.id, a.customer_name as customerName, a.appointment_date as appointmentDate,
             a.appointment_time as appointmentTime, a.status,
             s.name as serviceName, s.price
      FROM appointments a
      JOIN services s ON a.service_id = s.id
      WHERE a.business_id = ? AND a.appointment_date >= CURDATE()
      AND a.status IN ('pending', 'confirmed')
      ORDER BY a.appointment_date ASC, a.appointment_time ASC
      LIMIT 10
    `, [req.user.businessId]);

    // Get monthly appointment counts for chart
    const monthlyData = await executeQuery(`
      SELECT 
        DATE_FORMAT(appointment_date, '%Y-%m') as month,
        COUNT(*) as appointment_count,
        SUM(CASE WHEN status IN ('confirmed', 'completed') THEN s.price ELSE 0 END) as revenue
      FROM appointments a
      JOIN services s ON a.service_id = s.id
      WHERE a.business_id = ? 
      AND a.appointment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(appointment_date, '%Y-%m')
      ORDER BY month DESC
      LIMIT 12
    `, [req.user.businessId]);

    // Get service popularity
    const serviceStats = await executeQuery(`
      SELECT 
        s.name as serviceName,
        COUNT(a.id) as booking_count,
        SUM(CASE WHEN a.status IN ('confirmed', 'completed') THEN s.price ELSE 0 END) as revenue
      FROM services s
      LEFT JOIN appointments a ON s.id = a.service_id
      WHERE s.business_id = ? AND s.is_active = 1
      GROUP BY s.id, s.name
      ORDER BY booking_count DESC
    `, [req.user.businessId]);

    const dashboardStats = {
      overview: stats[0] || {
        total_services: 0,
        total_appointments: 0,
        confirmed_appointments: 0,
        pending_appointments: 0,
        completed_appointments: 0,
        today_appointments: 0,
        today_confirmed: 0,
        total_revenue: 0,
        monthly_revenue: 0
      },
      recentAppointments,
      upcomingAppointments,
      monthlyData: monthlyData.reverse(), // Show oldest to newest for charts
      serviceStats
    };

    res.json(dashboardStats);

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard statistics',
      message: 'An error occurred while fetching dashboard statistics'
    });
  }
});

// Get business by slug (public endpoint)
router.get('/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    const business = await executeQuery(`
      SELECT id, name, slug, description, address, phone, email, website, 
             business_hours as businessHours, timezone
      FROM businesses 
      WHERE slug = ? AND is_active = 1
    `, [slug]);

    if (business.length === 0) {
      return res.status(404).json({
        error: 'Business not found',
        message: 'The requested business does not exist'
      });
    }

    const businessData = business[0];
    if (businessData.businessHours) {
      try {
        businessData.businessHours = JSON.parse(businessData.businessHours);
      } catch (e) {
        businessData.businessHours = null;
      }
    }

    res.json(businessData);

  } catch (error) {
    console.error('Get business by slug error:', error);
    res.status(500).json({
      error: 'Failed to fetch business',
      message: 'An error occurred while fetching the business'
    });
  }
});

module.exports = router;
