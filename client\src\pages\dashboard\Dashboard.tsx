import React, { useEffect, useState } from 'react';
import { Calendar, Clock, Users, ArrowRight } from 'lucide-react';
import DashboardLayout from '../../components/layout/DashboardLayout';
import { getAppointments, Appointment } from '../../api/appointments';
import { Link } from 'react-router-dom';
const Dashboard: React.FC = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        const data = await getAppointments();
        setAppointments(data);
      } catch (error) {
        console.error('Failed to fetch appointments:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchAppointments();
  }, []);
  // Get today's appointments
  const today = new Date().toISOString().split('T')[0];
  const todayAppointments = appointments.filter(app => app.appointmentDate === today);
  // Get upcoming appointments (excluding today)
  const upcomingAppointments = appointments.filter(app => app.appointmentDate > today).sort((a, b) => a.appointmentDate.localeCompare(b.appointmentDate)).slice(0, 5);
  const stats = [{
    name: "Today's Appointments",
    value: todayAppointments.length,
    icon: <Calendar className="h-6 w-6 text-primary" />,
    color: 'bg-blue-50'
  }, {
    name: 'Pending Confirmations',
    value: appointments.filter(app => app.status === 'pending').length,
    icon: <Clock className="h-6 w-6 text-yellow-500" />,
    color: 'bg-yellow-50'
  }, {
    name: 'Total Customers',
    value: new Set(appointments.map(app => app.customerEmail)).size,
    icon: <Users className="h-6 w-6 text-green-500" />,
    color: 'bg-green-50'
  }];
  return <DashboardLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        {/* Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          {stats.map(stat => <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 rounded-md p-3 ${stat.color}`}>
                    {stat.icon}
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd>
                        <div className="text-lg font-medium text-gray-900">
                          {stat.value}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>)}
        </div>
        {/* Today's Appointments */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Today's Appointments
            </h3>
            <Link to="/dashboard/appointments" className="text-sm font-medium text-primary hover:text-primary-focus flex items-center">
              View all <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          <div className="border-t border-gray-200">
            {loading ? <div className="p-4 text-center">Loading...</div> : todayAppointments.length > 0 ? <ul className="divide-y divide-gray-200">
                {todayAppointments.map(appointment => <li key={appointment.id} className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="bg-gray-200 rounded-full h-10 w-10 flex items-center justify-center">
                          <span className="text-gray-700 font-medium">
                            {appointment.customerName.charAt(0)}
                          </span>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">
                            {appointment.customerName}
                          </p>
                          <p className="text-sm text-gray-500">
                            {appointment.serviceName}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900 mr-4">
                          {appointment.appointmentTime}
                        </div>
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' : appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                          {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  </li>)}
              </ul> : <div className="p-4 text-center text-gray-500">
                No appointments scheduled for today
              </div>}
          </div>
        </div>
        {/* Upcoming Appointments */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Upcoming Appointments
            </h3>
          </div>
          <div className="border-t border-gray-200">
            {loading ? <div className="p-4 text-center">Loading...</div> : upcomingAppointments.length > 0 ? <ul className="divide-y divide-gray-200">
                {upcomingAppointments.map(appointment => <li key={appointment.id} className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="bg-gray-200 rounded-full h-10 w-10 flex items-center justify-center">
                          <span className="text-gray-700 font-medium">
                            {appointment.customerName.charAt(0)}
                          </span>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">
                            {appointment.customerName}
                          </p>
                          <p className="text-sm text-gray-500">
                            {appointment.serviceName}
                          </p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-900">
                          {new Date(appointment.appointmentDate).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    })}
                        </p>
                        <p className="text-sm text-gray-500">
                          {appointment.appointmentTime}
                        </p>
                      </div>
                    </div>
                  </li>)}
              </ul> : <div className="p-4 text-center text-gray-500">
                No upcoming appointments
              </div>}
          </div>
        </div>
      </div>
    </DashboardLayout>;
};
export default Dashboard;