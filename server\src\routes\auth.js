const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body } = require('express-validator');
const { executeQuery, getConnection } = require('../config/database');
const { handleValidationErrors } = require('../middleware/errorHandler');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const registerValidation = [
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  body('businessName').trim().isLength({ min: 2, max: 100 }).withMessage('Business name must be between 2 and 100 characters')
];

const loginValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required')
];

// Helper function to generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Helper function to create business slug
const createSlug = (businessName) => {
  return businessName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
};

// Register endpoint
router.post('/register', registerValidation, handleValidationErrors, async (req, res) => {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    const { name, email, password, businessName } = req.body;

    // Check if user already exists
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length > 0) {
      return res.status(409).json({
        error: 'Registration failed',
        message: 'User with this email already exists'
      });
    }

    // Create business slug and check for uniqueness
    let slug = createSlug(businessName);
    let slugCounter = 1;
    
    while (true) {
      const existingBusiness = await executeQuery(
        'SELECT id FROM businesses WHERE slug = ?',
        [slug]
      );
      
      if (existingBusiness.length === 0) break;
      
      slug = `${createSlug(businessName)}-${slugCounter}`;
      slugCounter++;
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create business
    const businessResult = await connection.execute(
      'INSERT INTO businesses (name, slug, description) VALUES (?, ?, ?)',
      [businessName, slug, `Welcome to ${businessName}! Book your appointment today.`]
    );

    const businessId = businessResult[0].insertId;

    // Create user
    const userResult = await connection.execute(
      'INSERT INTO users (name, email, password_hash, business_id, role) VALUES (?, ?, ?, ?, ?)',
      [name, email, passwordHash, businessId, 'owner']
    );

    const userId = userResult[0].insertId;

    await connection.commit();

    // Generate token
    const token = generateToken(userId);

    // Return user data (without password)
    res.status(201).json({
      message: 'Registration successful',
      user: {
        id: userId,
        name,
        email,
        businessName,
        slug
      },
      token
    });

  } catch (error) {
    await connection.rollback();
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  } finally {
    connection.release();
  }
});

// Login endpoint
router.post('/login', loginValidation, handleValidationErrors, async (req, res) => {
  try {
    const { email, password } = req.body;

    // Get user with business info
    const users = await executeQuery(`
      SELECT u.*, b.name as business_name, b.slug as business_slug 
      FROM users u 
      LEFT JOIN businesses b ON u.business_id = b.id 
      WHERE u.email = ? AND u.is_active = 1
    `, [email]);

    if (users.length === 0) {
      return res.status(401).json({
        error: 'Login failed',
        message: 'Invalid email or password'
      });
    }

    const user = users[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Login failed',
        message: 'Invalid email or password'
      });
    }

    // Update last login
    await executeQuery(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
      [user.id]
    );

    // Generate token
    const token = generateToken(user.id);

    // Return user data (without password)
    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        businessName: user.business_name,
        slug: user.business_slug
      },
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

// Get current user endpoint
router.get('/me', authenticateToken, async (req, res) => {
  try {
    res.json({
      user: {
        id: req.user.id,
        name: req.user.name,
        email: req.user.email,
        businessName: req.user.businessName,
        slug: req.user.businessSlug
      }
    });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({
      error: 'Failed to get user data',
      message: 'An error occurred while fetching user information'
    });
  }
});

// Logout endpoint (client-side token removal, but we can track it server-side if needed)
router.post('/logout', authenticateToken, (req, res) => {
  res.json({
    message: 'Logout successful'
  });
});

module.exports = router;
