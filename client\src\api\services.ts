// Mock API functions for services
export interface Service {
  id: number;
  name: string;
  price: number;
  durationMinutes: number;
}
// Mock services data
const mockServices: Service[] = [{
  id: 1,
  name: 'Haircut',
  price: 250,
  durationMinutes: 30
}, {
  id: 2,
  name: 'Beard Trim',
  price: 150,
  durationMinutes: 15
}, {
  id: 3,
  name: 'Hair Coloring',
  price: 800,
  durationMinutes: 90
}, {
  id: 4,
  name: 'Shave',
  price: 200,
  durationMinutes: 20
}];
export const getServices = async (): Promise<Service[]> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return [...mockServices];
};
export const createService = async (service: Omit<Service, 'id'>): Promise<Service> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const newService = {
    ...service,
    id: Math.max(0, ...mockServices.map(s => s.id)) + 1
  };
  mockServices.push(newService);
  return newService;
};
export const updateService = async (id: number, service: Partial<Service>): Promise<Service> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const index = mockServices.findIndex(s => s.id === id);
  if (index === -1) {
    throw new Error('Service not found');
  }
  mockServices[index] = {
    ...mockServices[index],
    ...service
  };
  return mockServices[index];
};
export const deleteService = async (id: number): Promise<void> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const index = mockServices.findIndex(s => s.id === id);
  if (index !== -1) {
    mockServices.splice(index, 1);
  }
};