import { api } from './config';

export interface Service {
  id: number;
  name: string;
  description?: string;
  price: number;
  durationMinutes: number;
  isActive?: boolean;
  displayOrder?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateServiceData {
  name: string;
  description?: string;
  price: number;
  durationMinutes: number;
}

// Get services for authenticated business owner
export const getServices = async (): Promise<Service[]> => {
  const response = await api.get('/services');
  return response;
};

// Get services for public booking page
export const getServicesByBusinessSlug = async (businessSlug: string): Promise<Service[]> => {
  const response = await api.get(`/services/business/${businessSlug}`, false);
  return response;
};

// Get single service
export const getService = async (id: number): Promise<Service> => {
  const response = await api.get(`/services/${id}`);
  return response;
};

// Create new service
export const createService = async (serviceData: CreateServiceData): Promise<Service> => {
  const response = await api.post('/services', serviceData);
  return response.service;
};

// Update service
export const updateService = async (id: number, serviceData: CreateServiceData): Promise<Service> => {
  const response = await api.put(`/services/${id}`, serviceData);
  return response.service;
};

// Toggle service active status
export const toggleServiceStatus = async (id: number): Promise<{ isActive: boolean }> => {
  const response = await api.patch(`/services/${id}/toggle`, {});
  return response;
};

// Delete service
export const deleteService = async (id: number): Promise<void> => {
  await api.delete(`/services/${id}`);
};
export const createService = async (service: Omit<Service, 'id'>): Promise<Service> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const newService = {
    ...service,
    id: Math.max(0, ...mockServices.map(s => s.id)) + 1
  };
  mockServices.push(newService);
  return newService;
};
export const updateService = async (id: number, service: Partial<Service>): Promise<Service> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const index = mockServices.findIndex(s => s.id === id);
  if (index === -1) {
    throw new Error('Service not found');
  }
  mockServices[index] = {
    ...mockServices[index],
    ...service
  };
  return mockServices[index];
};
export const deleteService = async (id: number): Promise<void> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const index = mockServices.findIndex(s => s.id === id);
  if (index !== -1) {
    mockServices.splice(index, 1);
  }
};