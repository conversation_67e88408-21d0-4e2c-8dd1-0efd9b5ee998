import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
// Public Pages
import LandingPage from './pages/public/LandingPage';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import PublicBooking from './pages/public/PublicBooking';
// Dashboard Pages
import Dashboard from './pages/dashboard/Dashboard';
import Services from './pages/dashboard/Services';
import Appointments from './pages/dashboard/Appointments';
import Settings from './pages/dashboard/Settings';
export function App() {
  return <Router>
      <AuthProvider>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* Landing Page */}
            <Route path="/" element={<LandingPage />} />
            {/* Auth Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            {/* Public Booking Route */}
            <Route path="/book/:businessSlug" element={<PublicBooking />} />
            {/* Protected Dashboard Routes */}
            <Route path="/dashboard" element={<ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>} />
            <Route path="/dashboard/services" element={<ProtectedRoute>
                  <Services />
                </ProtectedRoute>} />
            <Route path="/dashboard/appointments" element={<ProtectedRoute>
                  <Appointments />
                </ProtectedRoute>} />
            <Route path="/dashboard/settings" element={<ProtectedRoute>
                  <Settings />
                </ProtectedRoute>} />
            {/* Redirect root to dashboard or login */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </AuthProvider>
    </Router>;
}