{"name": "iskedyulko-backend", "version": "1.0.0", "description": "Backend API for IskedyulKo appointment booking system", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "keywords": ["appointment", "booking", "api", "express", "mysql"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}