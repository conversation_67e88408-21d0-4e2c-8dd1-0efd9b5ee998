// Mock API functions for authentication
// These would be replaced with actual API calls in a real application
interface User {
  id: number;
  name: string;
  email: string;
  businessName: string;
  slug: string;
}
interface RegisterData {
  name: string;
  email: string;
  password: string;
  businessName: string;
}
interface AuthResponse {
  user: User;
  token: string;
}
// Mock user data
const mockUser: User = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  businessName: "Juan's Barbershop",
  slug: 'juans-barbershop'
};
export const loginUser = async (email: string, password: string): Promise<AuthResponse> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  // In a real app, this would be a fetch call to your backend
  if (email === '<EMAIL>' && password === 'password') {
    return {
      user: mockUser,
      token: 'mock-jwt-token'
    };
  }
  // For demo purposes, let's accept any credentials
  return {
    user: {
      ...mockUser,
      email
    },
    token: 'mock-jwt-token'
  };
};
export const registerUser = async (userData: RegisterData): Promise<AuthResponse> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));
  // In a real app, this would be a fetch call to your backend
  return {
    user: {
      id: 1,
      name: userData.name,
      email: userData.email,
      businessName: userData.businessName,
      slug: userData.businessName.toLowerCase().replace(/\s+/g, '-')
    },
    token: 'mock-jwt-token'
  };
};
export const getCurrentUser = async (): Promise<User> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 300));
  // In a real app, this would validate the JWT token and return the user
  return mockUser;
};