import { api } from './config';

export interface User {
  id: number;
  name: string;
  email: string;
  businessName: string;
  slug: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  businessName: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  message: string;
}

export const loginUser = async (email: string, password: string): Promise<AuthResponse> => {
  const response = await api.post('/auth/login', { email, password }, false);
  return response;
};

export const registerUser = async (userData: RegisterData): Promise<AuthResponse> => {
  const response = await api.post('/auth/register', userData, false);
  return response;
};

export const getCurrentUser = async (): Promise<User> => {
  const response = await api.get('/auth/me');
  return response.user;
};

export const logoutUser = async (): Promise<void> => {
  await api.post('/auth/logout', {});
};