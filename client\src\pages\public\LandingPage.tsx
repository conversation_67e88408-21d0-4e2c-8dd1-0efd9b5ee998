import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Calendar, Clock, Users, CheckCircle, Star, ArrowRight, Menu, X } from 'lucide-react';
const LandingPage: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const features = [{
    icon: <Calendar className="h-6 w-6 text-primary" />,
    title: 'Easy Scheduling',
    description: 'Allow clients to book appointments online 24/7 without phone calls'
  }, {
    icon: <Clock className="h-6 w-6 text-primary" />,
    title: 'Time Management',
    description: 'Efficiently manage your schedule and reduce no-shows with automatic reminders'
  }, {
    icon: <Users className="h-6 w-6 text-primary" />,
    title: 'Client Management',
    description: 'Keep track of client information and booking history in one place'
  }, {
    icon: <CheckCircle className="h-6 w-6 text-primary" />,
    title: 'Service Customization',
    description: 'Create and customize services with pricing and duration that fit your business'
  }];
  const testimonials = [{
    quote: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> has transformed how I manage appointments at my salon. My clients love the easy booking process!',
    author: '<PERSON>',
    business: 'Bella Hair Salon',
    rating: 5
  }, {
    quote: "Since using this platform, I've reduced no-shows by 60% and saved hours each week on administrative tasks.",
    author: '<PERSON> <PERSON>',
    business: "Juan's Barbershop",
    rating: 5
  }, {
    quote: 'The dashboard is intuitive and my customers find the booking process simple and convenient.',
    author: 'Ana Lim',
    business: 'Wellness Spa Center',
    rating: 4
  }];
  const steps = [{
    number: '01',
    title: 'Create an account',
    description: 'Sign up for IskedyulKo in minutes and set up your business profile'
  }, {
    number: '02',
    title: 'Add your services',
    description: 'Input your services with prices, duration, and availability'
  }, {
    number: '03',
    title: 'Share your booking page',
    description: 'Send your unique booking link to clients or embed it on your website'
  }, {
    number: '04',
    title: 'Manage bookings',
    description: 'Accept, reschedule, or cancel appointments from your dashboard'
  }];
  return <div className="min-h-screen bg-white">
      {/* Header/Navigation */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0 flex items-center">
              <span className="text-xl font-bold text-primary">IskedyulKo</span>
            </div>
            {/* Desktop Navigation */}
            <nav className="hidden md:ml-6 md:flex md:space-x-8">
              <a href="#features" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                Features
              </a>
              <a href="#how-it-works" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                How It Works
              </a>
              <a href="#testimonials" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                Testimonials
              </a>
            </nav>
            {/* Login/Register Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              <Link to="/login" className="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                Log in
              </Link>
              <Link to="/register" className="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-opacity-90">
                Sign up free
              </Link>
            </div>
            {/* Mobile menu button */}
            <div className="flex md:hidden">
              <button onClick={() => setMobileMenuOpen(!mobileMenuOpen)} className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>
        {/* Mobile Navigation Menu */}
        {mobileMenuOpen && <div className="md:hidden bg-white border-b border-gray-200">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <a href="#features" className="block px-3 py-2 text-base font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                Features
              </a>
              <a href="#how-it-works" className="block px-3 py-2 text-base font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                How It Works
              </a>
              <a href="#testimonials" className="block px-3 py-2 text-base font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                Testimonials
              </a>
              <Link to="/login" className="block px-3 py-2 text-base font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                Log in
              </Link>
              <Link to="/register" className="block px-3 py-2 text-base font-medium text-primary hover:bg-gray-50 rounded-md">
                Sign up free
              </Link>
            </div>
          </div>}
      </header>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 leading-tight">
                Simplify Booking for Your Small Business
              </h1>
              <p className="mt-4 text-xl text-gray-600 max-w-lg">
                IskedyulKo helps you manage appointments, reduce no-shows, and
                provide a seamless booking experience for your clients.
              </p>
              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                <Link to="/register" className="px-6 py-3 bg-primary text-white font-medium rounded-md hover:bg-opacity-90 text-center">
                  Get Started Free
                </Link>
                <a href="#how-it-works" className="px-6 py-3 bg-white border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 text-center">
                  Learn How It Works
                </a>
              </div>
            </div>
            <div className="hidden md:block">
              <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1567&q=80" alt="Booking dashboard" className="rounded-lg shadow-xl" />
            </div>
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              Everything You Need to Manage Bookings
            </h2>
            <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
              IskedyulKo provides powerful tools designed specifically for small
              businesses to streamline appointment scheduling.
            </p>
          </div>
          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => <div key={index} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                <div className="bg-gray-50 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-medium text-gray-900">
                  {feature.title}
                </h3>
                <p className="mt-2 text-gray-600">{feature.description}</p>
              </div>)}
          </div>
        </div>
      </section>
      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              How IskedyulKo Works
            </h2>
            <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
              Get up and running in minutes with our simple setup process.
            </p>
          </div>
          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {steps.map((step, index) => <div key={index} className="relative">
                <div className="text-4xl font-bold text-gray-200">
                  {step.number}
                </div>
                <h3 className="mt-2 text-lg font-medium text-gray-900">
                  {step.title}
                </h3>
                <p className="mt-2 text-gray-600">{step.description}</p>
                {index < steps.length - 1 && <div className="hidden lg:block absolute top-10 left-full w-16 h-1 bg-gray-200 -ml-8 transform -translate-x-full">
                    <ArrowRight className="absolute -right-4 -top-2 h-5 w-5 text-gray-300" />
                  </div>}
              </div>)}
          </div>
        </div>
      </section>
      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              What Our Customers Say
            </h2>
            <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
              Join thousands of businesses that trust IskedyulKo for their
              scheduling needs.
            </p>
          </div>
          <div className="mt-16 grid gap-8 md:grid-cols-3">
            {testimonials.map((testimonial, index) => <div key={index} className="bg-gray-50 p-6 rounded-lg">
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => <Star key={i} className={`h-5 w-5 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />)}
                </div>
                <p className="text-gray-600 italic">"{testimonial.quote}"</p>
                <div className="mt-4">
                  <p className="font-medium text-gray-900">
                    {testimonial.author}
                  </p>
                  <p className="text-sm text-gray-500">
                    {testimonial.business}
                  </p>
                </div>
              </div>)}
          </div>
        </div>
      </section>
      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white">
            Ready to streamline your booking process?
          </h2>
          <p className="mt-4 text-xl text-white opacity-90 max-w-2xl mx-auto">
            Join IskedyulKo today and provide your clients with a seamless
            booking experience.
          </p>
          <div className="mt-8">
            <Link to="/register" className="px-8 py-3 bg-white text-primary font-medium rounded-md hover:bg-opacity-90 inline-block">
              Sign Up For Free
            </Link>
          </div>
          <p className="mt-4 text-sm text-white opacity-75">
            No credit card required. Free plan available.
          </p>
        </div>
      </section>
      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-bold mb-4">IskedyulKo</h3>
              <p className="text-gray-400">
                The simple scheduling solution for small businesses in the
                Philippines.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-4">Product</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#features" className="text-gray-400 hover:text-white">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#how-it-works" className="text-gray-400 hover:text-white">
                    How It Works
                  </a>
                </li>
                <li>
                  <a href="#testimonials" className="text-gray-400 hover:text-white">
                    Testimonials
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-4">Support</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white">
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-4">Account</h4>
              <ul className="space-y-2">
                <li>
                  <Link to="/login" className="text-gray-400 hover:text-white">
                    Log In
                  </Link>
                </li>
                <li>
                  <Link to="/register" className="text-gray-400 hover:text-white">
                    Sign Up
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm">
            <p>
              &copy; {new Date().getFullYear()} IskedyulKo. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>;
};
export default LandingPage;