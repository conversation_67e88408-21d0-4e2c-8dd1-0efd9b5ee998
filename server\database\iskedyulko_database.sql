-- IskedyulKo Database Schema
-- Import this file into phpMyAdmin to create the database structure

-- Create database
CREATE DATABASE IF NOT EXISTS `iskedyulko_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `iskedyulko_db`;

-- --------------------------------------------------------
-- Table structure for table `businesses`
-- --------------------------------------------------------

CREATE TABLE `businesses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL UNIQUE,
  `description` text,
  `address` text,
  `phone` varchar(20),
  `email` varchar(255),
  `website` varchar(255),
  `business_hours` json,
  `timezone` varchar(50) DEFAULT 'Asia/Manila',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `users`
-- --------------------------------------------------------

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL UNIQUE,
  `password_hash` varchar(255) NOT NULL,
  `business_id` int(11) DEFAULT NULL,
  `role` enum('owner','staff','customer') DEFAULT 'owner',
  `is_active` tinyint(1) DEFAULT 1,
  `email_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_role` (`role`),
  CONSTRAINT `fk_users_business` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `services`
-- --------------------------------------------------------

CREATE TABLE `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `duration_minutes` int(11) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_display_order` (`display_order`),
  CONSTRAINT `fk_services_business` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `appointments`
-- --------------------------------------------------------

CREATE TABLE `appointments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_phone` varchar(20),
  `appointment_date` date NOT NULL,
  `appointment_time` time NOT NULL,
  `end_time` time NOT NULL,
  `status` enum('pending','confirmed','cancelled','completed','no_show') DEFAULT 'pending',
  `notes` text,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_service_id` (`service_id`),
  KEY `idx_appointment_date` (`appointment_date`),
  KEY `idx_status` (`status`),
  KEY `idx_customer_email` (`customer_email`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `fk_appointments_business` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_appointments_service` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_appointments_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `business_settings`
-- --------------------------------------------------------

CREATE TABLE `business_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_business_setting` (`business_id`, `setting_key`),
  CONSTRAINT `fk_business_settings_business` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Insert sample data
-- --------------------------------------------------------

-- Sample business
INSERT INTO `businesses` (`name`, `slug`, `description`, `address`, `phone`, `email`, `business_hours`, `timezone`) VALUES
('Juan\'s Barbershop', 'juans-barbershop', 'Professional barbershop services at affordable prices', '123 Main Street, Manila, Philippines', '+63 ************', '<EMAIL>', '{"monday": {"open": "09:00", "close": "18:00"}, "tuesday": {"open": "09:00", "close": "18:00"}, "wednesday": {"open": "09:00", "close": "18:00"}, "thursday": {"open": "09:00", "close": "18:00"}, "friday": {"open": "09:00", "close": "18:00"}, "saturday": {"open": "08:00", "close": "17:00"}, "sunday": {"closed": true}}', 'Asia/Manila');

-- Sample user (password is 'password123' hashed with bcrypt)
INSERT INTO `users` (`name`, `email`, `password_hash`, `business_id`, `role`) VALUES
('Juan Dela Cruz', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 'owner');

-- Sample services
INSERT INTO `services` (`business_id`, `name`, `description`, `price`, `duration_minutes`, `display_order`) VALUES
(1, 'Haircut', 'Professional haircut with styling', 250.00, 30, 1),
(1, 'Beard Trim', 'Beard trimming and shaping', 150.00, 15, 2),
(1, 'Hair Coloring', 'Professional hair coloring service', 800.00, 90, 3),
(1, 'Shave', 'Traditional wet shave', 200.00, 20, 4);

-- Sample appointments
INSERT INTO `appointments` (`business_id`, `service_id`, `customer_name`, `customer_email`, `customer_phone`, `appointment_date`, `appointment_time`, `end_time`, `status`) VALUES
(1, 1, 'Maria Santos', '<EMAIL>', '+63 ************', '2024-01-15', '10:00:00', '10:30:00', 'confirmed'),
(1, 2, 'Pedro Reyes', '<EMAIL>', '+63 ************', '2024-01-15', '11:00:00', '11:15:00', 'pending'),
(1, 3, 'Ana Lim', '<EMAIL>', '+63 ************', '2024-01-16', '14:00:00', '15:30:00', 'confirmed');

-- --------------------------------------------------------
-- Create indexes for better performance
-- --------------------------------------------------------

-- Composite indexes for common queries
CREATE INDEX `idx_appointments_business_date` ON `appointments` (`business_id`, `appointment_date`);
CREATE INDEX `idx_appointments_business_status` ON `appointments` (`business_id`, `status`);
CREATE INDEX `idx_services_business_active` ON `services` (`business_id`, `is_active`);

-- --------------------------------------------------------
-- Create views for common queries
-- --------------------------------------------------------

-- View for appointment details with service information
CREATE VIEW `appointment_details` AS
SELECT
    a.id,
    a.business_id,
    a.customer_name,
    a.customer_email,
    a.customer_phone,
    a.appointment_date,
    a.appointment_time,
    a.end_time,
    a.status,
    a.notes,
    a.created_at,
    a.updated_at,
    s.id as service_id,
    s.name as service_name,
    s.price as service_price,
    s.duration_minutes as service_duration,
    b.name as business_name,
    b.slug as business_slug
FROM appointments a
JOIN services s ON a.service_id = s.id
JOIN businesses b ON a.business_id = b.id;

-- View for business statistics
CREATE VIEW `business_stats` AS
SELECT
    b.id as business_id,
    b.name as business_name,
    COUNT(DISTINCT s.id) as total_services,
    COUNT(DISTINCT a.id) as total_appointments,
    COUNT(DISTINCT CASE WHEN a.status = 'confirmed' THEN a.id END) as confirmed_appointments,
    COUNT(DISTINCT CASE WHEN a.status = 'pending' THEN a.id END) as pending_appointments,
    COUNT(DISTINCT CASE WHEN a.appointment_date = CURDATE() THEN a.id END) as today_appointments,
    COALESCE(SUM(CASE WHEN a.status IN ('confirmed', 'completed') THEN s.price END), 0) as total_revenue
FROM businesses b
LEFT JOIN services s ON b.id = s.business_id AND s.is_active = 1
LEFT JOIN appointments a ON b.id = a.business_id
GROUP BY b.id, b.name;
