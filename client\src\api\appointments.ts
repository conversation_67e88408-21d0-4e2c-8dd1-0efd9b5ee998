import { api } from './config';

export interface Appointment {
  id: number;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  serviceId: number;
  serviceName: string;
  price?: number;
  durationMinutes?: number;
  appointmentDate: string;
  appointmentTime: string;
  endTime?: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show';
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateAppointmentData {
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  serviceId: number;
  appointmentDate: string;
  appointmentTime: string;
}

export interface AppointmentFilters {
  date?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

// Get appointments for business owner
export const getAppointments = async (filters?: AppointmentFilters): Promise<Appointment[]> => {
  const queryParams = new URLSearchParams();

  if (filters?.date) queryParams.append('date', filters.date);
  if (filters?.status) queryParams.append('status', filters.status);
  if (filters?.limit) queryParams.append('limit', filters.limit.toString());
  if (filters?.offset) queryParams.append('offset', filters.offset.toString());

  const endpoint = `/appointments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  const response = await api.get(endpoint);
  return response;
};

// Get single appointment
export const getAppointment = async (id: number): Promise<Appointment> => {
  const response = await api.get(`/appointments/${id}`);
  return response;
};

// Create appointment (public booking)
export const createAppointment = async (businessSlug: string, appointmentData: CreateAppointmentData): Promise<Appointment> => {
  const response = await api.post(`/appointments/book/${businessSlug}`, appointmentData, false);
  return response.appointment;
};

// Update appointment status
export const updateAppointmentStatus = async (id: number, status: Appointment['status']): Promise<void> => {
  await api.patch(`/appointments/${id}/status`, { status });
};

// Update appointment notes
export const updateAppointmentNotes = async (id: number, notes: string): Promise<void> => {
  await api.patch(`/appointments/${id}/notes`, { notes });
};

// Delete appointment
export const deleteAppointment = async (id: number): Promise<void> => {
  await api.delete(`/appointments/${id}`);
};

// Get available time slots
export const getAvailableTimeSlots = async (businessSlug: string, serviceId: number, date: string): Promise<{ date: string; availableSlots: string[] }> => {
  const response = await api.get(`/appointments/availability/${businessSlug}/${serviceId}/${date}`, false);
  return response;
};