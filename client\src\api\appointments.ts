// Mock API functions for appointments
export interface Appointment {
  id: number;
  customerName: string;
  customerEmail: string;
  serviceId: number;
  serviceName: string;
  appointmentDate: string;
  appointmentTime: string;
  status: 'pending' | 'confirmed' | 'cancelled';
}
// Mock appointments data
const mockAppointments: Appointment[] = [{
  id: 1,
  customerName: '<PERSON>',
  customerEmail: '<EMAIL>',
  serviceId: 1,
  serviceName: 'Haircut',
  appointmentDate: '2023-10-25',
  appointmentTime: '10:00',
  status: 'confirmed'
}, {
  id: 2,
  customerName: '<PERSON>',
  customerEmail: '<EMAIL>',
  serviceId: 2,
  serviceName: 'Beard Trim',
  appointmentDate: '2023-10-25',
  appointmentTime: '11:00',
  status: 'pending'
}, {
  id: 3,
  customerName: 'Ana Lim',
  customerEmail: '<EMAIL>',
  serviceId: 3,
  serviceName: 'Hair Coloring',
  appointmentDate: '2023-10-26',
  appointmentTime: '14:00',
  status: 'confirmed'
}];
export const getAppointments = async (): Promise<Appointment[]> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return [...mockAppointments];
};
export const createAppointment = async (appointment: Omit<Appointment, 'id' | 'status'>): Promise<Appointment> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 700));
  const newAppointment = {
    ...appointment,
    id: Math.max(0, ...mockAppointments.map(a => a.id)) + 1,
    status: 'pending' as const
  };
  mockAppointments.push(newAppointment);
  return newAppointment;
};
export const updateAppointmentStatus = async (id: number, status: 'pending' | 'confirmed' | 'cancelled'): Promise<Appointment> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  const index = mockAppointments.findIndex(a => a.id === id);
  if (index === -1) {
    throw new Error('Appointment not found');
  }
  mockAppointments[index] = {
    ...mockAppointments[index],
    status
  };
  return mockAppointments[index];
};