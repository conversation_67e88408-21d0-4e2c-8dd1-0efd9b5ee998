const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        error: 'Access denied',
        message: 'No token provided' 
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database to ensure they still exist
    const user = await executeQuery(
      'SELECT u.*, b.name as business_name, b.slug as business_slug FROM users u LEFT JOIN businesses b ON u.business_id = b.id WHERE u.id = ?',
      [decoded.userId]
    );

    if (user.length === 0) {
      return res.status(401).json({ 
        error: 'Access denied',
        message: 'User not found' 
      });
    }

    // Add user info to request object
    req.user = {
      id: user[0].id,
      email: user[0].email,
      name: user[0].name,
      businessId: user[0].business_id,
      businessName: user[0].business_name,
      businessSlug: user[0].business_slug
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Access denied',
        message: 'Invalid token' 
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Access denied',
        message: 'Token expired' 
      });
    }
    
    console.error('Auth middleware error:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Authentication failed' 
    });
  }
};

// Middleware to check if user owns a business
const requireBusiness = (req, res, next) => {
  if (!req.user.businessId) {
    return res.status(403).json({
      error: 'Access denied',
      message: 'Business account required'
    });
  }
  next();
};

module.exports = {
  authenticateToken,
  requireBusiness
};
