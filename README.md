# IskedyulKo - Appointment Booking System

A full-stack appointment booking system built with React (frontend) and Express.js + MySQL (backend).

## Features

- **User Authentication**: Register/Login for business owners
- **Business Management**: Manage business profile and settings
- **Service Management**: Create, update, and manage services
- **Appointment Booking**: Public booking page for customers
- **Appointment Management**: View, confirm, cancel appointments
- **Dashboard**: Statistics and overview of business performance

## Tech Stack

### Frontend
- React 18 with TypeScript
- Vite for development
- Tailwind CSS for styling
- React Router for navigation
- Lucide React for icons

### Backend
- Node.js with Express.js
- MySQL database
- JWT authentication
- bcryptjs for password hashing
- express-validator for input validation

## Quick Setup

### Prerequisites
- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- phpMyAdmin (optional, for database management)

### 1. Database Setup

1. **Create Database**: 
   - Open phpMyAdmin or MySQL command line
   - Import the SQL file: `server/database/iskedyulko_database.sql`
   - This will create the database `iskedyulko_db` with all required tables and sample data

2. **Configure Database Connection**:
   - Copy `server/.env.example` to `server/.env`
   - Update the database credentials in `server/.env`:
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=iskedyulko_db
   DB_USER=your_mysql_username
   DB_PASSWORD=your_mysql_password
   ```

### 2. Backend Setup

```bash
cd server
npm install
npm run dev
```

The backend will start on `http://localhost:5000`

### 3. Frontend Setup

```bash
cd client
npm install
npm run dev
```

The frontend will start on `http://localhost:5173`

## Default Login Credentials

After importing the database, you can use these credentials:
- **Email**: `<EMAIL>`
- **Password**: `password123`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new business owner
- `POST /api/auth/login` - Login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout

### Services
- `GET /api/services` - Get all services (authenticated)
- `GET /api/services/business/:slug` - Get services by business slug (public)
- `POST /api/services` - Create service
- `PUT /api/services/:id` - Update service
- `DELETE /api/services/:id` - Delete service

### Appointments
- `GET /api/appointments` - Get appointments (authenticated)
- `POST /api/appointments/book/:businessSlug` - Book appointment (public)
- `PATCH /api/appointments/:id/status` - Update appointment status
- `GET /api/appointments/availability/:businessSlug/:serviceId/:date` - Get available time slots

### Business
- `GET /api/business/profile` - Get business profile
- `PUT /api/business/profile` - Update business profile
- `GET /api/business/stats` - Get dashboard statistics
- `GET /api/business/:slug` - Get business by slug (public)

## Project Structure

```
IskedyulKo/
├── client/                 # React frontend
│   ├── src/
│   │   ├── api/           # API service functions
│   │   ├── components/    # Reusable components
│   │   ├── contexts/      # React contexts
│   │   ├── pages/         # Page components
│   │   └── ...
│   └── package.json
├── server/                # Express.js backend
│   ├── src/
│   │   ├── config/        # Database configuration
│   │   ├── middleware/    # Express middleware
│   │   ├── routes/        # API routes
│   │   └── index.js       # Server entry point
│   ├── database/          # SQL schema and migrations
│   └── package.json
└── README.md
```

## Development

### Running in Development Mode

1. Start the backend: `cd server && npm run dev`
2. Start the frontend: `cd client && npm run dev`
3. Open `http://localhost:5173` in your browser

### Building for Production

1. Build frontend: `cd client && npm run build`
2. Start backend: `cd server && npm start`

## Troubleshooting

### Database Connection Issues
- Ensure MySQL is running
- Check database credentials in `server/.env`
- Verify the database `iskedyulko_db` exists
- Check if the user has proper permissions

### Port Conflicts
- Backend runs on port 5000 by default
- Frontend runs on port 5173 by default
- Change ports in `.env` files if needed

### CORS Issues
- Ensure `CLIENT_URL` in `server/.env` matches your frontend URL
- Default is `http://localhost:5173`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.
